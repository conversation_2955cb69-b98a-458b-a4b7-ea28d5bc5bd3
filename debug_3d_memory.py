#!/usr/bin/env python3

import json
import tempfile
import duckdb

def debug_3d_array():
    # Create the exact same data as the failing test
    data = [[[1, 2], [3, 4]], [[5, 6], [7, 8]]]
    
    print("Original data structure:")
    print(f"Root array length: {len(data)}")
    for i, row in enumerate(data):
        print(f"  Row {i}: {row} (length: {len(row)})")
        for j, sub_array in enumerate(row):
            print(f"    Sub-array {j}: {sub_array} (length: {len(sub_array)})")
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(data, f)
        temp_file = f.name
    
    print(f"\nJSON file created: {temp_file}")
    
    # Test with DuckDB
    conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
    conn.execute("LOAD '/Users/<USER>/projects/duckdb-json-extension/extension-template-rs/build/debug/streaming_json_reader.duckdb_extension'")
    
    try:
        result = conn.execute(f'SELECT * FROM streaming_json_reader("{temp_file}")').fetchall()
        print(f"\nResult: {result}")
        print(f"Number of rows: {len(result)}")
        
        for i, row in enumerate(result):
            print(f"Row {i}: {row}")
            if len(row) > 0:
                first_col = row[0]
                print(f"  First column type: {type(first_col)}")
                print(f"  First column value: {first_col}")
                if isinstance(first_col, list):
                    print(f"  First column length: {len(first_col)}")
                    for j, item in enumerate(first_col):
                        print(f"    Item {j}: {item} (type: {type(item)})")
                        if isinstance(item, list):
                            print(f"      Sub-item length: {len(item)}")
                            for k, sub_item in enumerate(item):
                                print(f"        Sub-item {k}: {sub_item}")
    
    except Exception as e:
        print(f"Error: {e}")
    
    # Clean up
    import os
    os.unlink(temp_file)

if __name__ == "__main__":
    debug_3d_array()
