#!/usr/bin/env python3

import duckdb
import json
import tempfile
import os

def test_simple_struct_array():
    """Test a simple array of structs to debug the crash."""
    
    # Create a simple test case
    data = {
        "users": [
            {"id": 1, "name": "<PERSON>"},
            {"id": 2, "name": "<PERSON>"}
        ]
    }
    
    # Write to temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(data, f)
        temp_file = f.name
    
    try:
        # Connect to DuckDB and load extension
        conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
        conn.execute('LOAD "build/debug/streaming_json_reader.duckdb_extension"')
        
        print("Testing simple struct array...")
        print(f"Data: {data}")
        
        # Try to query the data
        try:
            result = conn.execute(f'SELECT users FROM streaming_json_reader("{temp_file}")').fetchall()
            print(f"Success! Result: {result}")
        except Exception as e:
            print(f"Error during query: {e}")
            
        # Try to describe the schema
        try:
            schema = conn.execute(f'DESCRIBE SELECT * FROM streaming_json_reader("{temp_file}")').fetchall()
            print(f"Schema: {schema}")
        except Exception as e:
            print(f"Error during schema query: {e}")
            
    finally:
        os.unlink(temp_file)

if __name__ == "__main__":
    test_simple_struct_array()
