#!/usr/bin/env python3

import json
import tempfile
import duckdb

def debug_stack_processor():
    # Create a simpler 2D array first to understand the pattern
    data = [[1, 2], [3, 4]]
    
    print("2D Array test:")
    print(f"Data: {data}")
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(data, f)
        temp_file = f.name
    
    print(f"JSON file created: {temp_file}")
    
    # Test with DuckDB
    conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
    conn.execute("LOAD '/Users/<USER>/projects/duckdb-json-extension/extension-template-rs/build/debug/streaming_json_reader.duckdb_extension'")
    
    try:
        result = conn.execute(f'SELECT * FROM streaming_json_reader("{temp_file}")').fetchall()
        print(f"2D Result: {result}")
        
        # Now test 3D array
        print("\n" + "="*50)
        print("3D Array test:")
        
        data_3d = [[[1, 2], [3, 4]], [[5, 6], [7, 8]]]
        print(f"Data: {data_3d}")
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(data_3d, f)
            temp_file_3d = f.name
        
        result_3d = conn.execute(f'SELECT * FROM streaming_json_reader("{temp_file_3d}")').fetchall()
        print(f"3D Result: {result_3d}")
        
        # Analyze the pattern
        print("\nAnalysis:")
        print("Expected 3D result:")
        print("  Row 0: [[1, 2], [3, 4]]")
        print("  Row 1: [[5, 6], [7, 8]]")
        print("Actual 3D result:")
        for i, row in enumerate(result_3d):
            print(f"  Row {i}: {row[0]}")
    
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
    
    # Clean up
    import os
    os.unlink(temp_file)
    if 'temp_file_3d' in locals():
        os.unlink(temp_file_3d)

if __name__ == "__main__":
    debug_stack_processor()
