#!/usr/bin/env python3

import duckdb
import json
import tempfile
import os

def test_simple_2d():
    # Create a simple 2D array test
    data = [[1, 2], [3, 4]]
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(data, f)
        temp_file = f.name
    
    try:
        conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
        conn.execute("LOAD './build/debug/streaming_json_reader.duckdb_extension'")
        
        result = conn.execute(f'SELECT * FROM streaming_json_reader("{temp_file}")').fetchall()
        print("Simple 2D result:", result)
        print("Expected: Two rows with [1, 2] and [3, 4]")
        
        # Should have 2 rows (root array flattening)
        assert len(result) == 2
        
        # First row should contain [1, 2]
        first_row = result[0][0]
        print("First row:", first_row)
        assert first_row == [1, 2], f"Expected [1, 2], got {first_row}"
        
        # Second row should contain [3, 4]
        second_row = result[1][0]
        print("Second row:", second_row)
        assert second_row == [3, 4], f"Expected [3, 4], got {second_row}"
        
        print("Simple 2D test PASSED!")
        
    finally:
        os.unlink(temp_file)

def test_3d_minimal():
    # Create a minimal 3D array test
    data = [[[1, 2]]]
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(data, f)
        temp_file = f.name
    
    try:
        conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
        conn.execute("LOAD './build/debug/streaming_json_reader.duckdb_extension'")
        
        result = conn.execute(f'SELECT * FROM streaming_json_reader("{temp_file}")').fetchall()
        print("Minimal 3D result:", result)
        print("Expected: One row with [[1, 2]]")
        
        # Should have 1 row (root array flattening)
        assert len(result) == 1
        
        # First row should contain [[1, 2]]
        first_row = result[0][0]
        print("First row:", first_row)
        assert len(first_row) == 1
        assert first_row[0] == [1, 2], f"Expected [1, 2], got {first_row[0]}"
        
        print("Minimal 3D test PASSED!")
        
    finally:
        os.unlink(temp_file)

if __name__ == "__main__":
    print("Testing simple 2D array...")
    test_simple_2d()
    
    print("\nTesting minimal 3D array...")
    test_3d_minimal()
