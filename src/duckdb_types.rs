use duckdb::core::{LogicalT<PERSON><PERSON><PERSON><PERSON>, LogicalTypeId};
use crate::types::{JsonType, ProcessingMode};

/// Convert a JsonType to a DuckDB LogicalTypeHandle
pub fn json_type_to_duckdb_type(json_type: &JsonType) -> Result<LogicalTypeHandle, Box<dyn std::error::Error>> {
    match json_type {
        JsonType::Null => Ok(LogicalTypeHandle::from(LogicalTypeId::Varchar)), // Nullable VARCHAR
        JsonType::Boolean => Ok(LogicalTypeHandle::from(LogicalTypeId::Boolean)),
        JsonType::Integer => Ok(LogicalTypeHandle::from(LogicalTypeId::Bigint)),
        JsonType::Number => Ok(LogicalTypeHandle::from(LogicalTypeId::Double)),
        JsonType::String => Ok(LogicalTypeHandle::from(LogicalTypeId::Varchar)),
        JsonType::Array { element_type } => {
            let child_type = json_type_to_duckdb_type(element_type)?;
            Ok(LogicalTypeHandle::list(&child_type))
        }
        JsonType::Object { fields } => {
            let mut struct_fields = Vec::new();
            for (field_name, field_type) in fields {
                let field_logical_type = json_type_to_duckdb_type(field_type)?;
                struct_fields.push((field_name.as_str(), field_logical_type));
            }
            Ok(LogicalTypeHandle::struct_type(&struct_fields))
        }
    }
}

/// Generate column definitions for a JSON schema based on processing mode
pub fn generate_column_definitions(
    root_type: &JsonType,
    processing_mode: &ProcessingMode,
) -> Result<Vec<(String, LogicalTypeHandle)>, Box<dyn std::error::Error>> {
    match processing_mode {
        ProcessingMode::SingleObject => {
            // Single object - each field becomes a column
            if let JsonType::Object { fields } = root_type {
                let mut columns = Vec::new();
                for (field_name, field_type) in fields {
                    let logical_type = json_type_to_duckdb_type(field_type)?;
                    columns.push((field_name.clone(), logical_type));
                }
                Ok(columns)
            } else {
                Err("Expected object type for SingleObject processing mode".into())
            }
        }
        ProcessingMode::ArrayOfObjects => {
            // Array of objects - flatten object fields into columns
            if let JsonType::Array { element_type } = root_type {
                if let JsonType::Object { fields } = element_type.as_ref() {
                    let mut columns = Vec::new();
                    for (field_name, field_type) in fields {
                        let logical_type = json_type_to_duckdb_type(field_type)?;
                        columns.push((field_name.clone(), logical_type));
                    }
                    Ok(columns)
                } else {
                    Err("Expected array of objects for ArrayOfObjects processing mode".into())
                }
            } else {
                Err("Expected array type for ArrayOfObjects processing mode".into())
            }
        }
        ProcessingMode::ArrayOfPrimitives => {
            // Array of primitives - single column named "value"
            if let JsonType::Array { element_type } = root_type {
                let logical_type = json_type_to_duckdb_type(element_type)?;
                Ok(vec![("value".to_string(), logical_type)])
            } else {
                Err("Expected array type for ArrayOfPrimitives processing mode".into())
            }
        }
        ProcessingMode::SinglePrimitive => {
            // Single primitive - single column named "value"
            let logical_type = json_type_to_duckdb_type(root_type)?;
            Ok(vec![("value".to_string(), logical_type)])
        }
    }
}

/// Check if a JSON type is nullable (contains null values)
pub fn is_nullable_type(json_type: &JsonType) -> bool {
    match json_type {
        JsonType::Null => true,
        JsonType::Array { element_type } => is_nullable_type(element_type),
        JsonType::Object { fields } => {
            fields.iter().any(|(_, field_type)| is_nullable_type(field_type))
        }
        _ => false,
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_primitive_type_conversion() {
        assert!(matches!(
            json_type_to_duckdb_type(&JsonType::Boolean).unwrap().id(),
            LogicalTypeId::Boolean
        ));
        
        assert!(matches!(
            json_type_to_duckdb_type(&JsonType::Integer).unwrap().id(),
            LogicalTypeId::Bigint
        ));
        
        assert!(matches!(
            json_type_to_duckdb_type(&JsonType::Number).unwrap().id(),
            LogicalTypeId::Double
        ));
        
        assert!(matches!(
            json_type_to_duckdb_type(&JsonType::String).unwrap().id(),
            LogicalTypeId::Varchar
        ));
    }

    #[test]
    fn test_array_type_conversion() {
        let array_type = JsonType::Array {
            element_type: Box::new(JsonType::Integer),
        };
        
        let duckdb_type = json_type_to_duckdb_type(&array_type).unwrap();
        assert!(matches!(duckdb_type.id(), LogicalTypeId::List));
    }

    #[test]
    fn test_object_type_conversion() {
        let object_type = JsonType::Object {
            fields: vec![
                ("name".to_string(), JsonType::String),
                ("age".to_string(), JsonType::Integer),
            ],
        };
        
        let duckdb_type = json_type_to_duckdb_type(&object_type).unwrap();
        assert!(matches!(duckdb_type.id(), LogicalTypeId::Struct));
    }

    #[test]
    fn test_column_definitions_array_of_objects() {
        let root_type = JsonType::Array {
            element_type: Box::new(JsonType::Object {
                fields: vec![
                    ("name".to_string(), JsonType::String),
                    ("age".to_string(), JsonType::Integer),
                ],
            }),
        };
        
        let columns = generate_column_definitions(&root_type, &ProcessingMode::ArrayOfObjects).unwrap();
        assert_eq!(columns.len(), 2);
        assert_eq!(columns[0].0, "name");
        assert_eq!(columns[1].0, "age");
    }

    #[test]
    fn test_column_definitions_array_of_primitives() {
        let root_type = JsonType::Array {
            element_type: Box::new(JsonType::Integer),
        };
        
        let columns = generate_column_definitions(&root_type, &ProcessingMode::ArrayOfPrimitives).unwrap();
        assert_eq!(columns.len(), 1);
        assert_eq!(columns[0].0, "value");
    }
}
