use std::fs::File;
use std::io::BufReader;
use struson::reader::{<PERSON>son<PERSON><PERSON>amReader, JsonReader};
use crate::types::{JsonSchema, ProcessingMode};

/// Streaming JSON parser that processes data in chunks
pub struct StreamingJsonParser {
    reader: JsonStreamReader<BufReader<File>>,
    schema: JsonSchema,
    current_position: ParserPosition,
    chunk_size: usize,
}

/// Current position in the JSON stream
#[derive(Debug, Clone)]
enum ParserPosition {
    /// At the beginning of the file
    Start,
    /// Inside an array, with current index
    InArray { index: usize },
    /// Processing a single object
    InObject,
    /// At the end of the file
    End,
}

/// A chunk of parsed JSON data ready for DuckDB insertion
#[derive(Debug)]
pub struct JsonChunk {
    /// The parsed JSON values for this chunk
    pub values: Vec<JsonValue>,
    /// Number of rows in this chunk
    pub row_count: usize,
    /// Whether this is the last chunk
    pub is_last: bool,
}

/// Parsed JSON value that can be inserted into DuckDB vectors
#[derive(Debug, <PERSON>lone)]
pub enum JsonValue {
    Null,
    Bo<PERSON>an(bool),
    Integer(i64),
    Number(f64),
    String(String),
    Array(Vec<JsonValue>),
    Object(Vec<(String, JsonValue)>),
}

impl StreamingJsonParser {
    /// Create a new streaming parser for the given file
    pub fn new(file_path: &str, schema: JsonSchema) -> Result<Self, Box<dyn std::error::Error>> {
        let file = File::open(file_path)?;
        let buf_reader = BufReader::new(file);
        let reader = create_json_reader(buf_reader);
        
        Ok(Self {
            reader,
            schema,
            current_position: ParserPosition::Start,
            chunk_size: 2048, // DuckDB's standard vector size
        })
    }

    /// Set the chunk size for processing
    pub fn set_chunk_size(&mut self, size: usize) {
        self.chunk_size = size;
    }

    /// Parse the next chunk of JSON data
    pub fn next_chunk(&mut self) -> Result<Option<JsonChunk>, Box<dyn std::error::Error>> {
        match &self.schema.processing_mode {
            ProcessingMode::SingleObject => self.parse_single_object_chunk(),
            ProcessingMode::ArrayOfObjects => self.parse_array_of_objects_chunk(),
            ProcessingMode::ArrayOfPrimitives => self.parse_array_of_primitives_chunk(),
            ProcessingMode::SinglePrimitive => self.parse_single_primitive_chunk(),
        }
    }

    /// Parse a single JSON object
    fn parse_single_object_chunk(&mut self) -> Result<Option<JsonChunk>, Box<dyn std::error::Error>> {
        if matches!(self.current_position, ParserPosition::End) {
            return Ok(None);
        }

        let value = self.parse_json_value()?;
        self.current_position = ParserPosition::End;

        Ok(Some(JsonChunk {
            values: vec![value],
            row_count: 1,
            is_last: true,
        }))
    }

    /// Parse a single primitive value
    fn parse_single_primitive_chunk(&mut self) -> Result<Option<JsonChunk>, Box<dyn std::error::Error>> {
        if matches!(self.current_position, ParserPosition::End) {
            return Ok(None);
        }

        let value = self.parse_json_value()?;
        self.current_position = ParserPosition::End;

        Ok(Some(JsonChunk {
            values: vec![value],
            row_count: 1,
            is_last: true,
        }))
    }

    /// Parse a chunk from an array of objects
    fn parse_array_of_objects_chunk(&mut self) -> Result<Option<JsonChunk>, Box<dyn std::error::Error>> {
        match &self.current_position {
            ParserPosition::Start => {
                self.reader.begin_array()?;
                self.current_position = ParserPosition::InArray { index: 0 };
            }
            ParserPosition::End => return Ok(None),
            _ => {}
        }

        let mut values = Vec::new();
        let mut row_count = 0;

        while self.reader.has_next()? && row_count < self.chunk_size {
            let value = self.parse_json_value()?;
            values.push(value);
            row_count += 1;

            if let ParserPosition::InArray { index } = &mut self.current_position {
                *index += 1;
            }
        }

        let is_last = !self.reader.has_next()?;
        if is_last {
            self.reader.end_array()?;
            self.current_position = ParserPosition::End;
        }

        if row_count == 0 {
            Ok(None)
        } else {
            Ok(Some(JsonChunk {
                values,
                row_count,
                is_last,
            }))
        }
    }

    /// Parse a chunk from an array of primitives
    fn parse_array_of_primitives_chunk(&mut self) -> Result<Option<JsonChunk>, Box<dyn std::error::Error>> {
        match &self.current_position {
            ParserPosition::Start => {
                self.reader.begin_array()?;
                self.current_position = ParserPosition::InArray { index: 0 };
            }
            ParserPosition::End => return Ok(None),
            _ => {}
        }

        let mut values = Vec::new();
        let mut row_count = 0;

        while self.reader.has_next()? && row_count < self.chunk_size {
            let value = self.parse_json_value()?;
            values.push(value);
            row_count += 1;

            if let ParserPosition::InArray { index } = &mut self.current_position {
                *index += 1;
            }
        }

        let is_last = !self.reader.has_next()?;
        if is_last {
            self.reader.end_array()?;
            self.current_position = ParserPosition::End;
        }

        if row_count == 0 {
            Ok(None)
        } else {
            Ok(Some(JsonChunk {
                values,
                row_count,
                is_last,
            }))
        }
    }

    /// Parse a single JSON value from the stream
    fn parse_json_value(&mut self) -> Result<JsonValue, Box<dyn std::error::Error>> {
        use struson::reader::ValueType;

        match self.reader.peek()? {
            ValueType::Null => {
                self.reader.next_null()?;
                Ok(JsonValue::Null)
            }
            ValueType::Boolean => {
                let value = self.reader.next_bool()?;
                Ok(JsonValue::Boolean(value))
            }
            ValueType::Number => {
                let number_str = self.reader.next_number_as_string()?;
                if number_str.contains('.') || number_str.contains('e') || number_str.contains('E') {
                    let value = number_str.parse::<f64>()?;
                    Ok(JsonValue::Number(value))
                } else {
                    let value = number_str.parse::<i64>()?;
                    Ok(JsonValue::Integer(value))
                }
            }
            ValueType::String => {
                let value = self.reader.next_string()?;
                Ok(JsonValue::String(value))
            }
            ValueType::Array => {
                self.parse_array_value()
            }
            ValueType::Object => {
                self.parse_object_value()
            }
        }
    }

    /// Parse an array value
    fn parse_array_value(&mut self) -> Result<JsonValue, Box<dyn std::error::Error>> {
        self.reader.begin_array()?;
        let mut elements = Vec::new();

        while self.reader.has_next()? {
            let element = self.parse_json_value()?;
            elements.push(element);
        }

        self.reader.end_array()?;
        Ok(JsonValue::Array(elements))
    }

    /// Parse an object value
    fn parse_object_value(&mut self) -> Result<JsonValue, Box<dyn std::error::Error>> {
        self.reader.begin_object()?;
        let mut fields = Vec::new();

        while self.reader.has_next()? {
            let field_name = self.reader.next_name()?.to_string();
            let field_value = self.parse_json_value()?;
            fields.push((field_name, field_value));
        }

        self.reader.end_object()?;
        Ok(JsonValue::Object(fields))
    }
}

/// Create a JsonStreamReader with appropriate settings for streaming
fn create_json_reader(reader: BufReader<File>) -> JsonStreamReader<BufReader<File>> {
    let settings = struson::reader::ReaderSettings {
        allow_comments: false,
        allow_trailing_comma: false,
        restrict_number_values: false,
        allow_multiple_top_level: false,
        max_nesting_depth: Some(64),
        track_path: false,
    };
    JsonStreamReader::new_custom(reader, settings)
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::types::JsonType;
    use std::io::Write;
    use tempfile::NamedTempFile;

    fn create_test_file(content: &str) -> NamedTempFile {
        let mut file = NamedTempFile::new().unwrap();
        file.write_all(content.as_bytes()).unwrap();
        file.flush().unwrap();
        file
    }

    #[test]
    fn test_parse_single_object() {
        let file = create_test_file(r#"{"name": "John", "age": 30}"#);
        let schema = JsonSchema::new(JsonType::Object {
            fields: vec![
                ("name".to_string(), JsonType::String),
                ("age".to_string(), JsonType::Integer),
            ],
        });

        let mut parser = StreamingJsonParser::new(file.path().to_str().unwrap(), schema).unwrap();
        let chunk = parser.next_chunk().unwrap().unwrap();

        assert_eq!(chunk.row_count, 1);
        assert!(chunk.is_last);
        assert!(matches!(chunk.values[0], JsonValue::Object(_)));
    }

    #[test]
    fn test_parse_array_of_primitives() {
        let file = create_test_file("[1, 2, 3, 4, 5]");
        let schema = JsonSchema::new(JsonType::Array {
            element_type: Box::new(JsonType::Integer),
        });

        let mut parser = StreamingJsonParser::new(file.path().to_str().unwrap(), schema).unwrap();
        parser.set_chunk_size(3);

        // First chunk
        let chunk1 = parser.next_chunk().unwrap().unwrap();
        assert_eq!(chunk1.row_count, 3);
        assert!(!chunk1.is_last);

        // Second chunk
        let chunk2 = parser.next_chunk().unwrap().unwrap();
        assert_eq!(chunk2.row_count, 2);
        assert!(chunk2.is_last);

        // No more chunks
        assert!(parser.next_chunk().unwrap().is_none());
    }
}
