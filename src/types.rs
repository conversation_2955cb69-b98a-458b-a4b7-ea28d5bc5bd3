use std::collections::HashMap;

/// JSON schema type representation for DuckDB column mapping
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, PartialEq)]
pub enum JsonType {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    Integer,
    Number,
    String,
    Array {
        element_type: Box<JsonType>,
    },
    Object {
        fields: Vec<(String, JsonType)>,
    },
}

impl JsonType {
    /// Check if this type can be unified with another type
    pub fn can_unify_with(&self, other: &JsonType) -> bool {
        match (self, other) {
            (JsonType::Null, _) | (_, JsonType::Null) => true,
            (JsonType::Integer, JsonType::Number) | (JsonType::Number, JsonType::Integer) => true,
            (JsonType::Array { element_type: a }, JsonType::Array { element_type: b }) => {
                a.can_unify_with(b)
            }
            (JsonType::Object { fields: a }, JsonType::Object { fields: b }) => {
                // Objects can unify if they have compatible field sets
                let a_map: HashMap<&String, &JsonType> = a.iter().map(|(k, v)| (k, v)).collect();
                let b_map: HashMap<&String, &JsonType> = b.iter().map(|(k, v)| (k, v)).collect();
                
                // Check all fields from both objects
                let all_fields: std::collections::HashSet<_> = a_map.keys().chain(b_map.keys()).collect();
                
                for field_name in all_fields {
                    match (a_map.get(field_name), b_map.get(field_name)) {
                        (Some(a_type), Some(b_type)) => {
                            if !a_type.can_unify_with(b_type) {
                                return false;
                            }
                        }
                        (None, Some(_)) | (Some(_), None) => {
                            // Missing fields are OK - they become nullable
                        }
                        (None, None) => {
                            // This shouldn't happen since we're iterating over keys from both maps
                            unreachable!()
                        }
                    }
                }
                true
            }
            _ => self == other,
        }
    }

    /// Unify this type with another type, returning the most general type
    pub fn unify_with(&self, other: &JsonType) -> JsonType {
        match (self, other) {
            (JsonType::Null, other) | (other, JsonType::Null) => other.clone(),
            (JsonType::Integer, JsonType::Number) | (JsonType::Number, JsonType::Integer) => JsonType::Number,
            (JsonType::Array { element_type: a }, JsonType::Array { element_type: b }) => {
                JsonType::Array {
                    element_type: Box::new(a.unify_with(b)),
                }
            }
            (JsonType::Object { fields: a }, JsonType::Object { fields: b }) => {
                let mut unified_fields = Vec::new();
                let a_map: HashMap<&String, &JsonType> = a.iter().map(|(k, v)| (k, v)).collect();
                let b_map: HashMap<&String, &JsonType> = b.iter().map(|(k, v)| (k, v)).collect();
                
                // Collect all field names
                let all_fields: std::collections::HashSet<_> = a_map.keys().chain(b_map.keys()).collect();
                
                for field_name in all_fields {
                    let unified_type = match (a_map.get(field_name), b_map.get(field_name)) {
                        (Some(a_type), Some(b_type)) => a_type.unify_with(b_type),
                        (Some(a_type), None) | (None, Some(a_type)) => {
                            // Missing field becomes nullable
                            JsonType::Null.unify_with(a_type)
                        }
                        (None, None) => unreachable!(),
                    };
                    unified_fields.push(((*field_name).clone(), unified_type));
                }
                
                // Sort fields for consistent ordering
                unified_fields.sort_by(|a, b| a.0.cmp(&b.0));
                
                JsonType::Object { fields: unified_fields }
            }
            _ if self == other => self.clone(),
            _ => {
                // Incompatible types - this shouldn't happen if can_unify_with was checked
                panic!("Cannot unify incompatible types: {:?} and {:?}", self, other);
            }
        }
    }
}

/// Schema inference configuration
#[derive(Debug, Clone)]
pub struct SchemaConfig {
    /// Maximum number of rows to examine for schema inference
    pub max_sample_rows: usize,
    /// Whether to enable debug output during schema inference
    pub debug: bool,
}

impl Default for SchemaConfig {
    fn default() -> Self {
        Self {
            max_sample_rows: 1000,
            debug: false,
        }
    }
}

/// Inferred schema for a JSON file
#[derive(Debug, Clone)]
pub struct JsonSchema {
    /// Root type of the JSON data
    pub root_type: JsonType,
    /// Processing mode based on root type
    pub processing_mode: ProcessingMode,
}

/// How to process the JSON data based on its structure
#[derive(Debug, Clone, PartialEq)]
pub enum ProcessingMode {
    /// Single JSON object -> one row with struct columns
    SingleObject,
    /// Array of objects -> multiple rows with flattened columns
    ArrayOfObjects,
    /// Array of primitives -> multiple rows with single column
    ArrayOfPrimitives,
    /// Single primitive -> one row with single column
    SinglePrimitive,
}

impl JsonSchema {
    pub fn new(root_type: JsonType) -> Self {
        let processing_mode = match &root_type {
            JsonType::Array { element_type } => {
                match element_type.as_ref() {
                    JsonType::Object { .. } => ProcessingMode::ArrayOfObjects,
                    _ => ProcessingMode::ArrayOfPrimitives,
                }
            }
            JsonType::Object { .. } => ProcessingMode::SingleObject,
            _ => ProcessingMode::SinglePrimitive,
        };

        Self {
            root_type,
            processing_mode,
        }
    }
}
