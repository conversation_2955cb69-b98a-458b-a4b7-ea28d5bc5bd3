use std::collections::HashSet;
use crate::types::{JsonType, ProcessingMode};

/// Projection information for optimizing JSON parsing
#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct ProjectionInfo {
    /// Columns that are actually needed by the query
    pub projected_columns: Vec<usize>,
    /// Field names that need to be parsed (for object types)
    pub projected_fields: HashSet<String>,
    /// Whether all columns are projected
    pub is_full_projection: bool,
}

impl ProjectionInfo {
    /// Create a new projection info with all columns projected
    pub fn full_projection(column_count: usize) -> Self {
        Self {
            projected_columns: (0..column_count).collect(),
            projected_fields: HashSet::new(),
            is_full_projection: true,
        }
    }

    /// Create a new projection info with specific columns
    pub fn partial_projection(
        projected_columns: Vec<usize>,
        schema: &JsonType,
        processing_mode: &ProcessingMode,
    ) -> Self {
        let projected_fields = Self::compute_projected_fields(&projected_columns, schema, processing_mode);
        
        Self {
            is_full_projection: false,
            projected_columns,
            projected_fields,
        }
    }

    /// Compute which JSON fields need to be parsed based on projected columns
    fn compute_projected_fields(
        projected_columns: &[usize],
        schema: &JsonType,
        processing_mode: &ProcessingMode,
    ) -> HashSet<String> {
        let mut fields = HashSet::new();

        match processing_mode {
            ProcessingMode::SingleObject => {
                if let JsonType::Object { fields: schema_fields } = schema {
                    for &col_idx in projected_columns {
                        if let Some((field_name, _)) = schema_fields.get(col_idx) {
                            fields.insert(field_name.clone());
                        }
                    }
                }
            }
            ProcessingMode::ArrayOfObjects => {
                if let JsonType::Array { element_type } = schema {
                    if let JsonType::Object { fields: schema_fields } = element_type.as_ref() {
                        for &col_idx in projected_columns {
                            if let Some((field_name, _)) = schema_fields.get(col_idx) {
                                fields.insert(field_name.clone());
                            }
                        }
                    }
                }
            }
            ProcessingMode::ArrayOfPrimitives | ProcessingMode::SinglePrimitive => {
                // For primitive types, there's only one "value" column
                // No field-level projection needed
            }
        }

        fields
    }

    /// Check if a field should be parsed based on projection
    pub fn should_parse_field(&self, field_name: &str) -> bool {
        if self.is_full_projection {
            true
        } else {
            self.projected_fields.contains(field_name)
        }
    }

    /// Check if a column is projected
    pub fn is_column_projected(&self, column_index: usize) -> bool {
        self.projected_columns.contains(&column_index)
    }

    /// Get the number of projected columns
    pub fn projected_column_count(&self) -> usize {
        self.projected_columns.len()
    }
}

/// Optimized JSON parser that skips unprojected fields
pub struct ProjectedJsonParser {
    projection: ProjectionInfo,
}

impl ProjectedJsonParser {
    pub fn new(projection: ProjectionInfo) -> Self {
        Self { projection }
    }

    /// Parse a JSON object, skipping fields that aren't projected
    pub fn parse_object_projected(
        &self,
        json_reader: &mut struson::reader::JsonStreamReader<std::io::BufReader<std::fs::File>>,
    ) -> Result<Vec<(String, crate::streaming_parser::JsonValue)>, Box<dyn std::error::Error>> {
        use struson::reader::JsonReader;
        
        json_reader.begin_object()?;
        let mut fields = Vec::new();

        while json_reader.has_next()? {
            let field_name = json_reader.next_name()?.to_string();
            
            if self.projection.should_parse_field(&field_name) {
                // Parse this field since it's projected
                let field_value = self.parse_json_value(json_reader)?;
                fields.push((field_name, field_value));
            } else {
                // Skip this field since it's not projected
                json_reader.skip_value()?;
            }
        }

        json_reader.end_object()?;
        Ok(fields)
    }

    /// Parse a JSON value (recursive helper)
    fn parse_json_value(
        &self,
        json_reader: &mut struson::reader::JsonStreamReader<std::io::BufReader<std::fs::File>>,
    ) -> Result<crate::streaming_parser::JsonValue, Box<dyn std::error::Error>> {
        use struson::reader::{JsonReader, ValueType};
        use crate::streaming_parser::JsonValue;

        match json_reader.peek()? {
            ValueType::Null => {
                json_reader.next_null()?;
                Ok(JsonValue::Null)
            }
            ValueType::Boolean => {
                let value = json_reader.next_bool()?;
                Ok(JsonValue::Boolean(value))
            }
            ValueType::Number => {
                let number_str = json_reader.next_number_as_string()?;
                if number_str.contains('.') || number_str.contains('e') || number_str.contains('E') {
                    let value = number_str.parse::<f64>()?;
                    Ok(JsonValue::Number(value))
                } else {
                    let value = number_str.parse::<i64>()?;
                    Ok(JsonValue::Integer(value))
                }
            }
            ValueType::String => {
                let value = json_reader.next_string()?;
                Ok(JsonValue::String(value))
            }
            ValueType::Array => {
                json_reader.begin_array()?;
                let mut elements = Vec::new();

                while json_reader.has_next()? {
                    let element = self.parse_json_value(json_reader)?;
                    elements.push(element);
                }

                json_reader.end_array()?;
                Ok(JsonValue::Array(elements))
            }
            ValueType::Object => {
                let fields = self.parse_object_projected(json_reader)?;
                Ok(JsonValue::Object(fields))
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::types::JsonType;

    #[test]
    fn test_full_projection() {
        let projection = ProjectionInfo::full_projection(3);
        assert!(projection.is_full_projection);
        assert_eq!(projection.projected_column_count(), 3);
        assert!(projection.is_column_projected(0));
        assert!(projection.is_column_projected(1));
        assert!(projection.is_column_projected(2));
    }

    #[test]
    fn test_partial_projection_object() {
        let schema = JsonType::Object {
            fields: vec![
                ("name".to_string(), JsonType::String),
                ("age".to_string(), JsonType::Integer),
                ("email".to_string(), JsonType::String),
            ],
        };

        let projection = ProjectionInfo::partial_projection(
            vec![0, 2], // Only name and email
            &schema,
            &ProcessingMode::SingleObject,
        );

        assert!(!projection.is_full_projection);
        assert_eq!(projection.projected_column_count(), 2);
        assert!(projection.should_parse_field("name"));
        assert!(!projection.should_parse_field("age"));
        assert!(projection.should_parse_field("email"));
    }

    #[test]
    fn test_partial_projection_array_of_objects() {
        let schema = JsonType::Array {
            element_type: Box::new(JsonType::Object {
                fields: vec![
                    ("id".to_string(), JsonType::Integer),
                    ("name".to_string(), JsonType::String),
                    ("active".to_string(), JsonType::Boolean),
                ],
            }),
        };

        let projection = ProjectionInfo::partial_projection(
            vec![1], // Only name
            &schema,
            &ProcessingMode::ArrayOfObjects,
        );

        assert!(!projection.is_full_projection);
        assert_eq!(projection.projected_column_count(), 1);
        assert!(!projection.should_parse_field("id"));
        assert!(projection.should_parse_field("name"));
        assert!(!projection.should_parse_field("active"));
    }

    #[test]
    fn test_primitive_projection() {
        let schema = JsonType::Array {
            element_type: Box::new(JsonType::Integer),
        };

        let projection = ProjectionInfo::partial_projection(
            vec![0], // Single value column
            &schema,
            &ProcessingMode::ArrayOfPrimitives,
        );

        assert!(!projection.is_full_projection);
        assert_eq!(projection.projected_column_count(), 1);
        // For primitives, field-level projection doesn't apply
        assert_eq!(projection.projected_fields.len(), 0);
    }
}
