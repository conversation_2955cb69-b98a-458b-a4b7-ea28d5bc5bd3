// DuckDB JSON Extension
// Streaming JSON reader with nested data support

pub mod types;
pub mod schema;
pub mod duckdb_types;
pub mod streaming_parser;
pub mod vector_writer;
pub mod projection;
pub mod vtab;

// Re-export main public API
pub use types::{JsonType, JsonSchema, SchemaConfig, ProcessingMode};
pub use schema::infer_schema_from_file;
pub use duckdb_types::{json_type_to_duckdb_type, generate_column_definitions};
pub use streaming_parser::{StreamingJsonParser, JsonChunk, JsonValue};
pub use vector_writer::VectorWriter;
pub use projection::{ProjectionInfo, ProjectedJsonParser};
pub use vtab::{JsonReaderVTab, streaming_json_reader_init};