use std::fs::File;
use std::io::BufReader;
use struson::reader::{JsonStreamReader, JsonReader};
use crate::types::{JsonType, JsonSchema, SchemaConfig};

/// Infer JSON schema from a file
pub fn infer_schema_from_file(file_path: &str, config: &SchemaConfig) -> Result<JsonSchema, Box<dyn std::error::Error>> {
    let file = File::open(file_path)?;
    let buf_reader = BufReader::new(file);
    let mut json_reader = create_json_reader(buf_reader);

    let root_type = infer_json_type(&mut json_reader, config)?;
    Ok(JsonSchema::new(root_type))
}

/// Create a JsonStreamReader with appropriate settings
fn create_json_reader(reader: BufReader<File>) -> JsonStreamReader<BufReader<File>> {
    let settings = struson::reader::ReaderSettings {
        allow_comments: false,
        allow_trailing_comma: false,
        restrict_number_values: false,
        allow_multiple_top_level: false,
        max_nesting_depth: Some(64), // Reasonable limit for nested JSON
        track_path: false,
    };
    JsonStreamReader::new_custom(reader, settings)
}

/// Infer the JSON type from a stream
fn infer_json_type(json_reader: &mut JsonStreamReader<BufReader<File>>, config: &SchemaConfig) -> Result<JsonType, Box<dyn std::error::Error>> {
    use struson::reader::ValueType;

    match json_reader.peek()? {
        ValueType::Null => {
            json_reader.next_null()?;
            Ok(JsonType::Null)
        }
        ValueType::Boolean => {
            json_reader.next_bool()?;
            Ok(JsonType::Boolean)
        }
        ValueType::Number => {
            let number = json_reader.next_number_as_string()?;
            // Try to determine if it's an integer or float
            if number.contains('.') || number.contains('e') || number.contains('E') {
                Ok(JsonType::Number)
            } else {
                Ok(JsonType::Integer)
            }
        }
        ValueType::String => {
            json_reader.next_string()?;
            Ok(JsonType::String)
        }
        ValueType::Array => {
            infer_array_type(json_reader, config)
        }
        ValueType::Object => {
            infer_object_type(json_reader, config)
        }
    }
}

/// Infer the type of an array by examining its elements
fn infer_array_type(json_reader: &mut JsonStreamReader<BufReader<File>>, config: &SchemaConfig) -> Result<JsonType, Box<dyn std::error::Error>> {
    json_reader.begin_array()?;

    let mut element_type: Option<JsonType> = None;
    let mut samples_examined = 0;

    while json_reader.has_next()? && samples_examined < config.max_sample_rows {
        let current_type = infer_json_type(json_reader, config)?;

        element_type = match element_type {
            None => Some(current_type),
            Some(existing_type) => {
                if existing_type.can_unify_with(&current_type) {
                    Some(existing_type.unify_with(&current_type))
                } else {
                    // Incompatible types - fall back to a more general type
                    if config.debug {
                        eprintln!("Warning: Incompatible array element types: {:?} vs {:?}", existing_type, current_type);
                    }
                    Some(JsonType::String) // Fallback to string for incompatible types
                }
            }
        };

        samples_examined += 1;
    }

    // Skip remaining elements if we hit the sample limit
    while json_reader.has_next()? {
        json_reader.skip_value()?;
    }

    json_reader.end_array()?;

    let element_type = element_type.unwrap_or(JsonType::Null);
    Ok(JsonType::Array {
        element_type: Box::new(element_type),
    })
}

/// Infer the type of an object by examining its fields
fn infer_object_type(json_reader: &mut JsonStreamReader<BufReader<File>>, config: &SchemaConfig) -> Result<JsonType, Box<dyn std::error::Error>> {
    json_reader.begin_object()?;

    let mut fields = Vec::new();

    while json_reader.has_next()? {
        let field_name = json_reader.next_name()?.to_string();
        let field_type = infer_json_type(json_reader, config)?;
        fields.push((field_name, field_type));
    }

    json_reader.end_object()?;

    // Sort fields for consistent ordering
    fields.sort_by(|a, b| a.0.cmp(&b.0));

    Ok(JsonType::Object { fields })
}

/// Infer schema from multiple JSON objects (for array of objects)
pub fn infer_unified_object_schema(
    json_reader: &mut JsonStreamReader<BufReader<File>>,
    config: &SchemaConfig
) -> Result<JsonType, Box<dyn std::error::Error>> {
    json_reader.begin_array()?;

    let mut unified_schema: Option<JsonType> = None;
    let mut objects_examined = 0;

    while json_reader.has_next()? && objects_examined < config.max_sample_rows {
        let object_type = infer_object_type(json_reader, config)?;

        unified_schema = match unified_schema {
            None => Some(object_type),
            Some(existing_schema) => {
                if existing_schema.can_unify_with(&object_type) {
                    Some(existing_schema.unify_with(&object_type))
                } else {
                    if config.debug {
                        eprintln!("Warning: Cannot unify object schemas");
                    }
                    return Err("Incompatible object schemas in array".into());
                }
            }
        };

        objects_examined += 1;
    }

    // Skip remaining objects if we hit the sample limit
    while json_reader.has_next()? {
        json_reader.skip_value()?;
    }

    json_reader.end_array()?;

    unified_schema.ok_or_else(|| "Empty array - cannot infer schema".into())
}