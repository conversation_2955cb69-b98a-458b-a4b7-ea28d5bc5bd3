use duckdb::core::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FlatVector, Inserter};
use crate::streaming_parser::{JsonValue, JsonChunk};
use crate::types::{JsonType, ProcessingMode};

/// Writer for populating DuckDB vectors with JSON data
pub struct VectorWriter {
    processing_mode: ProcessingMode,
}

impl VectorWriter {
    pub fn new(processing_mode: ProcessingMode) -> Self {
        Self { processing_mode }
    }

    /// Write a JSON chunk to DuckDB vectors
    pub fn write_chunk(
        &self,
        chunk: &JsonChunk,
        output: &mut DataChunkHandle,
        schema: &JsonType,
    ) -> Result<(), Box<dyn std::error::Error>> {
        match &self.processing_mode {
            ProcessingMode::SingleObject => self.write_single_object(chunk, output, schema),
            ProcessingMode::ArrayOfObjects => self.write_array_of_objects(chunk, output, schema),
            ProcessingMode::ArrayOfPrimitives => self.write_array_of_primitives(chunk, output, schema),
            ProcessingMode::SinglePrimitive => self.write_single_primitive(chunk, output, schema),
        }
    }

    /// Write a single object to DuckDB vectors
    fn write_single_object(
        &self,
        chunk: &JsonChunk,
        output: &mut DataChunkHandle,
        schema: &JsonType,
    ) -> Result<(), Box<dyn std::error::Error>> {
        if let JsonType::Object { fields } = schema {
            if let Some(JsonValue::Object(obj_fields)) = chunk.values.first() {
                // Create a map for quick field lookup
                let field_map: std::collections::HashMap<&String, &JsonValue> = obj_fields.iter().map(|(k, v)| (k, v)).collect();

                for (col_idx, (field_name, field_type)) in fields.iter().enumerate() {
                    if let Some(field_value) = field_map.get(field_name) {
                        self.write_value_to_vector_by_type(field_value, output, col_idx, 0, field_type)?;
                    } else {
                        // Field is missing - set as NULL based on type
                        match field_type {
                            JsonType::Object { .. } => {
                                // TODO: Set struct column as NULL
                                // For now, skip - DuckDB should handle this
                            }
                            JsonType::Array { .. } => {
                                let mut list_vector = output.list_vector(col_idx);
                                list_vector.set_null(0);
                                list_vector.set_entry(0, 0, 0);
                            }
                            _ => {
                                let mut flat_vector = output.flat_vector(col_idx);
                                flat_vector.set_null(0);
                            }
                        }
                    }
                }
            }
        }

        output.set_len(1);
        Ok(())
    }

    /// Write an array of objects to DuckDB vectors
    fn write_array_of_objects(
        &self,
        chunk: &JsonChunk,
        output: &mut DataChunkHandle,
        schema: &JsonType,
    ) -> Result<(), Box<dyn std::error::Error>> {
        if let JsonType::Array { element_type } = schema {
            if let JsonType::Object { fields } = element_type.as_ref() {
                // Handle each column separately
                for (col_idx, (field_name, field_type)) in fields.iter().enumerate() {
                    match field_type {
                        JsonType::Array { element_type } => {
                            // This column is a LIST - handle all rows at once
                            self.write_array_column_to_list_vector(chunk, output, col_idx, field_name, element_type)?;
                        }
                        JsonType::Object { fields: struct_fields } => {
                            // This column is a STRUCT - handle each row individually
                            let mut struct_vector = output.struct_vector(col_idx);
                            for (row_idx, value) in chunk.values.iter().enumerate() {
                                if let JsonValue::Object(obj_fields) = value {
                                    let field_map: std::collections::HashMap<&String, &JsonValue> = obj_fields.iter().map(|(k, v)| (k, v)).collect();
                                    if let Some(field_value) = field_map.get(field_name) {
                                        self.write_struct_value_to_vector(field_value, &mut struct_vector, row_idx, struct_fields)?;
                                    } else {
                                        // TODO: Set struct as NULL
                                    }
                                }
                            }
                        }
                        _ => {
                            // This column is a primitive - handle each row individually
                            let mut flat_vector = output.flat_vector(col_idx);
                            for (row_idx, value) in chunk.values.iter().enumerate() {
                                if let JsonValue::Object(obj_fields) = value {
                                    let field_map: std::collections::HashMap<&String, &JsonValue> = obj_fields.iter().map(|(k, v)| (k, v)).collect();
                                    if let Some(field_value) = field_map.get(field_name) {
                                        self.write_simple_value_to_vector(field_value, &mut flat_vector, row_idx)?;
                                    } else {
                                        flat_vector.set_null(row_idx);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        output.set_len(chunk.row_count);
        Ok(())
    }

    /// Write an array of primitives to DuckDB vectors
    fn write_array_of_primitives(
        &self,
        chunk: &JsonChunk,
        output: &mut DataChunkHandle,
        schema: &JsonType,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // Check if the "primitive" is actually a LIST type
        if let JsonType::Array { element_type } = schema {
            match element_type.as_ref() {
                JsonType::Array { .. } => {
                    // This is an array of arrays - use LIST vector
                    return self.write_array_of_lists(chunk, output, element_type);
                }
                JsonType::Object { .. } => {
                    // This is an array of objects - this should not happen in ArrayOfPrimitives mode
                    return Err("Array of objects should be handled by ArrayOfObjects processing mode, not ArrayOfPrimitives".into());
                }
                _ => {
                    // This is truly an array of primitives
                    let mut vector = output.flat_vector(0); // Single "value" column
                    for (row_idx, value) in chunk.values.iter().enumerate() {
                        self.write_simple_value_to_vector(value, &mut vector, row_idx)?;
                    }
                }
            }
        }

        output.set_len(chunk.row_count);
        Ok(())
    }

    /// Write a single primitive to DuckDB vectors
    fn write_single_primitive(
        &self,
        chunk: &JsonChunk,
        output: &mut DataChunkHandle,
        _schema: &JsonType,
    ) -> Result<(), Box<dyn std::error::Error>> {
        if let Some(value) = chunk.values.first() {
            let mut vector = output.flat_vector(0); // Single "value" column
            self.write_simple_value_to_vector(value, &mut vector, 0)?;
        }

        output.set_len(1);
        Ok(())
    }

    /// Write array values from multiple rows to a single LIST vector column
    fn write_array_column_to_list_vector(
        &self,
        chunk: &JsonChunk,
        output: &mut DataChunkHandle,
        col_idx: usize,
        field_name: &str,
        element_type: &JsonType,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let mut list_vector = output.list_vector(col_idx);

        // Collect all array values for this field from all rows
        let mut field_arrays = Vec::new();
        let empty_array = Vec::new(); // Create once to avoid lifetime issues
        for value in chunk.values.iter() {
            if let JsonValue::Object(obj_fields) = value {
                let field_map: std::collections::HashMap<&String, &JsonValue> = obj_fields.iter().map(|(k, v)| (k, v)).collect();
                if let Some(field_value) = field_map.get(&field_name.to_string()) {
                    if let JsonValue::Array(arr) = field_value {
                        field_arrays.push(arr);
                    } else {
                        return Err(format!("Expected array for field {}, got: {:?}", field_name, field_value).into());
                    }
                } else {
                    // Field is missing - treat as empty array
                    field_arrays.push(&empty_array);
                }
            }
        }

        // Count total child elements needed across all rows
        let total_child_elements: usize = field_arrays.iter().map(|arr| arr.len()).sum();
        list_vector.set_len(total_child_elements);

        // Write child elements based on element type
        match element_type {
            JsonType::Object { fields } => {
                // Array of objects - use struct child vector
                let mut struct_child = list_vector.struct_child(total_child_elements);
                let mut child_idx = 0;
                for arr in field_arrays.iter() {
                    for elem_value in arr.iter() {
                        self.write_struct_value_to_vector(elem_value, &mut struct_child, child_idx, fields)?;
                        child_idx += 1;
                    }
                }
            }
            JsonType::Array { element_type: nested_element_type } => {
                // Nested arrays - use list child vector
                let mut list_child = list_vector.list_child();
                let mut child_idx = 0;
                for arr in field_arrays.iter() {
                    for elem_value in arr.iter() {
                        self.write_list_value_to_vector(elem_value, &mut list_child, child_idx, nested_element_type)?;
                        child_idx += 1;
                    }
                }
            }
            _ => {
                // Array of primitives - use flat child vector
                let mut flat_child = list_vector.child(total_child_elements);
                let mut child_idx = 0;
                for arr in field_arrays.iter() {
                    for elem_value in arr.iter() {
                        self.write_simple_value_to_vector(elem_value, &mut flat_child, child_idx)?;
                        child_idx += 1;
                    }
                }
            }
        }

        // Set list entries (offset and length for each row)
        let mut current_offset = 0;
        for (row_idx, arr) in field_arrays.iter().enumerate() {
            list_vector.set_entry(row_idx, current_offset, arr.len());
            current_offset += arr.len();
        }

        Ok(())
    }

    /// Write an array of lists to DuckDB LIST vectors using proper DuckDB API
    fn write_array_of_lists(
        &self,
        chunk: &JsonChunk,
        output: &mut DataChunkHandle,
        element_type: &JsonType,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let mut list_vector = output.list_vector(0); // Single "value" column as LIST

        // Count total child elements needed across all rows
        let mut total_child_elements = 0;
        for value in chunk.values.iter() {
            if let JsonValue::Array(arr) = value {
                total_child_elements += arr.len();
            }
        }

        // Set the size of the child vector to accommodate all elements
        list_vector.set_len(total_child_elements);

        // Handle the child vector based on element type
        if let JsonType::Array { element_type: child_element_type } = element_type {
            match child_element_type.as_ref() {
                JsonType::Array { .. } => {
                    // Child elements are also arrays - get child as LIST vector
                    let child_list_vector = list_vector.list_child();
                    self.write_nested_arrays_to_list_vector(chunk, child_list_vector, child_element_type)?;
                }
                _ => {
                    // Child elements are primitives - get child as flat vector
                    let mut child_flat_vector = list_vector.child(total_child_elements);
                    self.write_primitive_arrays_to_flat_vector(chunk, &mut child_flat_vector)?;
                }
            }
        }

        // Set list entries (offset and length for each row)
        let mut current_offset = 0;
        for (row_idx, value) in chunk.values.iter().enumerate() {
            match value {
                JsonValue::Array(arr) => {
                    list_vector.set_entry(row_idx, current_offset, arr.len());
                    current_offset += arr.len();
                }
                JsonValue::Null => {
                    list_vector.set_null(row_idx);
                    list_vector.set_entry(row_idx, current_offset, 0);
                }
                _ => {
                    return Err(format!("Expected array value in array of lists, got: {:?}", value).into());
                }
            }
        }

        output.set_len(chunk.row_count);
        Ok(())
    }

    /// Write nested arrays to a LIST vector (for 3D+ arrays)
    fn write_nested_arrays_to_list_vector(
        &self,
        chunk: &JsonChunk,
        mut child_list_vector: duckdb::core::ListVector,
        child_element_type: &JsonType,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // Collect all child arrays from the parent arrays
        let mut child_arrays = Vec::new();
        for value in chunk.values.iter() {
            if let JsonValue::Array(arr) = value {
                for child_value in arr {
                    child_arrays.push(child_value.clone());
                }
            }
        }

        // Create a temporary chunk with the child arrays
        let child_chunk = JsonChunk {
            values: child_arrays,
            row_count: chunk.values.iter()
                .map(|v| if let JsonValue::Array(arr) = v { arr.len() } else { 0 })
                .sum(),
            is_last: false, // This is an intermediate chunk
        };

        // Recursively handle the child arrays
        self.write_list_vector_data(&child_chunk, &mut child_list_vector, child_element_type)
    }

    /// Write primitive arrays to a flat vector (for 2D arrays with primitive elements)
    fn write_primitive_arrays_to_flat_vector(
        &self,
        chunk: &JsonChunk,
        child_vector: &mut duckdb::core::FlatVector,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let mut child_idx = 0;
        for value in chunk.values.iter() {
            if let JsonValue::Array(arr) = value {
                for child_value in arr {
                    self.write_simple_value_to_vector(child_value, child_vector, child_idx)?;
                    child_idx += 1;
                }
            }
        }
        Ok(())
    }

    /// Generic method to write data to a LIST vector
    fn write_list_vector_data(
        &self,
        chunk: &JsonChunk,
        list_vector: &mut duckdb::core::ListVector,
        element_type: &JsonType,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // Count total child elements
        let mut total_child_elements = 0;
        for value in chunk.values.iter() {
            if let JsonValue::Array(arr) = value {
                total_child_elements += arr.len();
            }
        }

        // Set the size of the child vector
        list_vector.set_len(total_child_elements);

        // Handle child vector based on element type
        if let JsonType::Array { element_type: child_element_type } = element_type {
            match child_element_type.as_ref() {
                JsonType::Array { .. } => {
                    // Nested arrays - recurse with child LIST vector
                    let child_list_vector = list_vector.list_child();
                    self.write_nested_arrays_to_list_vector(chunk, child_list_vector, child_element_type)?;
                }
                _ => {
                    // Primitive elements - use flat vector
                    let mut child_flat_vector = list_vector.child(total_child_elements);
                    self.write_primitive_arrays_to_flat_vector(chunk, &mut child_flat_vector)?;
                }
            }
        }

        // Set list entries
        let mut current_offset = 0;
        for (row_idx, value) in chunk.values.iter().enumerate() {
            match value {
                JsonValue::Array(arr) => {
                    list_vector.set_entry(row_idx, current_offset, arr.len());
                    current_offset += arr.len();
                }
                JsonValue::Null => {
                    list_vector.set_null(row_idx);
                    list_vector.set_entry(row_idx, current_offset, 0);
                }
                _ => {
                    return Err(format!("Expected array value, got: {:?}", value).into());
                }
            }
        }

        Ok(())
    }



    /// Write a JSON value to a DuckDB vector with proper type handling
    fn write_simple_value_to_vector(
        &self,
        value: &JsonValue,
        vector: &mut FlatVector,
        row_idx: usize,
    ) -> Result<(), Box<dyn std::error::Error>> {
        match value {
            JsonValue::Null => {
                // Handle NULL values properly
                vector.set_null(row_idx);
            }
            JsonValue::Boolean(b) => {
                // For boolean, we need to write to the underlying data slice
                vector.as_mut_slice::<bool>()[row_idx] = *b;
            }
            JsonValue::Integer(i) => {
                // For integers, write to i64 slice
                vector.as_mut_slice::<i64>()[row_idx] = *i;
            }
            JsonValue::Number(n) => {
                // For numbers, write to f64 slice
                vector.as_mut_slice::<f64>()[row_idx] = *n;
            }
            JsonValue::String(s) => {
                // For strings, use the Inserter trait
                vector.insert(row_idx, s.as_str());
            }
            JsonValue::Array(_arr) => {
                return Err("Array values require LIST vector handling - this should be handled by write_list_value_to_vector".into());
            }
            JsonValue::Object(_) => {
                return Err("Object values require STRUCT vector handling - use write_struct_value_to_vector instead".into());
            }
        }
        Ok(())
    }

    /// Write a JSON value to the appropriate vector type based on the expected schema
    fn write_value_to_vector_by_type(
        &self,
        value: &JsonValue,
        output: &mut DataChunkHandle,
        col_idx: usize,
        row_idx: usize,
        expected_type: &JsonType,
    ) -> Result<(), Box<dyn std::error::Error>> {
        match expected_type {
            JsonType::Object { fields } => {
                // This column should be a STRUCT vector
                let mut struct_vector = output.struct_vector(col_idx);
                self.write_struct_value_to_vector(value, &mut struct_vector, row_idx, fields)?;
            }
            JsonType::Array { element_type } => {
                // This column should be a LIST vector
                let mut list_vector = output.list_vector(col_idx);
                self.write_single_array_to_list_vector(value, &mut list_vector, row_idx, element_type)?;
            }
            _ => {
                // This is a primitive type - use FlatVector
                let mut flat_vector = output.flat_vector(col_idx);
                self.write_simple_value_to_vector(value, &mut flat_vector, row_idx)?;
            }
        }
        Ok(())
    }

    /// Write a single array value to a LIST vector (for SingleObject mode)
    fn write_single_array_to_list_vector(
        &self,
        value: &JsonValue,
        list_vector: &mut duckdb::core::ListVector,
        row_idx: usize,
        element_type: &JsonType,
    ) -> Result<(), Box<dyn std::error::Error>> {
        match value {
            JsonValue::Array(arr) => {
                // Set the size of the child vector to accommodate this array
                list_vector.set_len(arr.len());

                // Write elements to child vector based on element type
                match element_type {
                    JsonType::Object { fields } => {
                        // Array of objects - use struct child vector
                        let mut struct_child = list_vector.struct_child(arr.len());
                        for (elem_idx, elem_value) in arr.iter().enumerate() {
                            self.write_struct_value_to_vector(elem_value, &mut struct_child, elem_idx, fields)?;
                        }
                    }
                    JsonType::Array { element_type: nested_element_type } => {
                        // Nested arrays - use list child vector
                        let mut list_child = list_vector.list_child();
                        for (elem_idx, elem_value) in arr.iter().enumerate() {
                            self.write_single_array_to_list_vector(elem_value, &mut list_child, elem_idx, nested_element_type)?;
                        }
                    }
                    _ => {
                        // Array of primitives - use flat child vector
                        let mut flat_child = list_vector.child(arr.len());
                        for (elem_idx, elem_value) in arr.iter().enumerate() {
                            self.write_simple_value_to_vector(elem_value, &mut flat_child, elem_idx)?;
                        }
                    }
                }

                // Set the list entry for this row (offset 0 since this is a single array)
                list_vector.set_entry(row_idx, 0, arr.len());
            }
            JsonValue::Null => {
                // Set list as NULL
                list_vector.set_null(row_idx);
                list_vector.set_entry(row_idx, 0, 0);
            }
            _ => {
                return Err(format!("Expected array or null for list field, got: {:?}", value).into());
            }
        }
        Ok(())
    }

    /// Write a JSON object to a STRUCT vector
    fn write_struct_value_to_vector(
        &self,
        value: &JsonValue,
        struct_vector: &mut duckdb::core::StructVector,
        row_idx: usize,
        expected_fields: &[(String, JsonType)],
    ) -> Result<(), Box<dyn std::error::Error>> {
        match value {
            JsonValue::Object(obj_fields) => {
                // Create a map for quick field lookup
                let field_map: std::collections::HashMap<&String, &JsonValue> =
                    obj_fields.iter().map(|(k, v)| (k, v)).collect();

                // Write each field to its corresponding child vector
                for (field_idx, (field_name, field_type)) in expected_fields.iter().enumerate() {
                    if let Some(field_value) = field_map.get(field_name) {
                        match field_type {
                            JsonType::Object { fields: nested_fields } => {
                                // Nested struct field
                                let mut nested_struct_vector = struct_vector.struct_vector_child(field_idx);
                                self.write_struct_value_to_vector(field_value, &mut nested_struct_vector, row_idx, nested_fields)?;
                            }
                            JsonType::Array { element_type } => {
                                // Array field in struct
                                let mut list_vector = struct_vector.list_vector_child(field_idx);
                                self.write_list_value_to_vector(field_value, &mut list_vector, row_idx, element_type)?;
                            }
                            _ => {
                                // Primitive field
                                let mut field_vector = struct_vector.child(field_idx, 1); // Capacity 1 for single value
                                self.write_simple_value_to_vector(field_value, &mut field_vector, row_idx)?;
                            }
                        }
                    } else {
                        // Field is missing - set as NULL
                        match field_type {
                            JsonType::Object { .. } => {
                                // TODO: Set struct as NULL - need to implement this
                            }
                            JsonType::Array { .. } => {
                                let mut list_vector = struct_vector.list_vector_child(field_idx);
                                list_vector.set_null(row_idx);
                            }
                            _ => {
                                let mut field_vector = struct_vector.child(field_idx, 1);
                                field_vector.set_null(row_idx);
                            }
                        }
                    }
                }
            }
            JsonValue::Null => {
                // TODO: Set entire struct as NULL - need to implement this
                // For now, set all fields as NULL
                for (field_idx, (_, field_type)) in expected_fields.iter().enumerate() {
                    match field_type {
                        JsonType::Object { .. } => {
                            // TODO: Set nested struct as NULL
                        }
                        JsonType::Array { .. } => {
                            let mut list_vector = struct_vector.list_vector_child(field_idx);
                            list_vector.set_null(row_idx);
                        }
                        _ => {
                            let mut field_vector = struct_vector.child(field_idx, 1);
                            field_vector.set_null(row_idx);
                        }
                    }
                }
            }
            _ => {
                return Err(format!("Expected object or null for struct field, got: {:?}", value).into());
            }
        }
        Ok(())
    }

    /// Write a JSON array to a LIST vector
    fn write_list_value_to_vector(
        &self,
        value: &JsonValue,
        list_vector: &mut duckdb::core::ListVector,
        row_idx: usize,
        element_type: &JsonType,
    ) -> Result<(), Box<dyn std::error::Error>> {
        match value {
            JsonValue::Array(arr) => {
                // Set the size of the child vector to accommodate this array
                list_vector.set_len(arr.len());

                // Write elements to child vector based on element type
                match element_type {
                    JsonType::Object { fields } => {
                        // Array of objects - use struct child vector
                        let mut struct_child = list_vector.struct_child(arr.len());
                        for (elem_idx, elem_value) in arr.iter().enumerate() {
                            self.write_struct_value_to_vector(elem_value, &mut struct_child, elem_idx, fields)?;
                        }
                    }
                    JsonType::Array { element_type: nested_element_type } => {
                        // Nested arrays - use list child vector
                        let mut list_child = list_vector.list_child();
                        for (elem_idx, elem_value) in arr.iter().enumerate() {
                            self.write_list_value_to_vector(elem_value, &mut list_child, elem_idx, nested_element_type)?;
                        }
                    }
                    _ => {
                        // Array of primitives - use flat child vector
                        let mut flat_child = list_vector.child(arr.len());
                        for (elem_idx, elem_value) in arr.iter().enumerate() {
                            self.write_simple_value_to_vector(elem_value, &mut flat_child, elem_idx)?;
                        }
                    }
                }

                // Set the list entry for this row
                list_vector.set_entry(row_idx, 0, arr.len());
            }
            JsonValue::Null => {
                // Set list as NULL
                list_vector.set_null(row_idx);
                list_vector.set_entry(row_idx, 0, 0);
            }
            _ => {
                return Err(format!("Expected array or null for list field, got: {:?}", value).into());
            }
        }
        Ok(())
    }
}
