// To build the Wasm target, a `staticlib` crate-type is required
//
// This is different than the default needed in native, and there is
// currently no way to select crate-type depending on target.
//
// This file sole purpose is remapping the content of lib as an
// example, do not change the content of the file.
//
// To build the Wasm target explicitly, use:
//   cargo build --example $PACKAGE_NAME

// Include all the modules directly
pub mod types;
pub mod schema;
pub mod duckdb_types;
pub mod streaming_parser;
pub mod vector_writer;
pub mod projection;
pub mod vtab;

// Re-export main public API
pub use types::{JsonType, JsonSchema, SchemaConfig, ProcessingMode};
pub use schema::infer_schema_from_file;
pub use duckdb_types::{json_type_to_duckdb_type, generate_column_definitions};
pub use streaming_parser::{StreamingJsonParser, JsonChunk, JsonValue};
pub use vector_writer::VectorWriter;
pub use projection::{ProjectionInfo, ProjectedJsonParser};
pub use vtab::{JsonReaderVTab, streaming_json_reader_init};
