Handling Deeply Nested Columns in DuckDB (Parquet & JSON)
DuckDB’s Nested Data Type Representation (Lists & Structs)
DuckDB supports nested types (comparable to Arrow’s nested arrays) using List and Struct vectors. A Struct in DuckDB contains multiple child vectors (one per field), but no direct data array of its own – you must retrieve each field’s vector with duckdb_struct_vector_get_child and fill them individually
duckdb.org
. The struct vector does have a validity mask to indicate if the entire struct is NULL (separately from individual field nulls)
duckdb.org
. A List in DuckDB is essentially a variable-length array per row. Internally, a list vector stores an array of duckdb_list_entry structs, each with an offset and length for that row’s list elements
duckdb.org
. All list elements across the column are stored contiguously in a single child vector (of the list’s element type). To access or populate a list’s elements, you first get the child vector via duckdb_list_vector_get_child. Then use duckdb_vector_get_data on the parent list vector to get the duckdb_list_entry array, where you set each entry’s offset and length
duckdb.org
. The child vector holds the actual elements; its length should equal the sum of all list lengths in the chunk. Both the list entries and the child elements have validity masks for NULLs (a list can be NULL itself, and elements can be NULL)
duckdb.org
duckdb.org
. Deep nesting (e.g. list of structs containing lists) is represented by nesting these concepts. For example, a column of type LIST<STRUCT{a: INT, b: LIST<INT>}> would be a list vector whose child is a struct vector (a and b fields). The b field itself is a list vector with its own child. In practice, you handle this by recursive vector access: e.g. get parent list’s child (struct vector), then get struct’s child for field b (a list vector), then get that list’s child for the actual integers, and so on. Each level has its offset/length array or multiple child vectors as defined above.
Parquet Reader’s Approach to Loading Nested Columns
DuckDB’s Parquet reader (C++ implementation) provides a good blueprint for handling nested data efficiently. It reads data in columnar, vectorized chunks rather than materializing everything at once, even for nested types. For a Parquet LIST column, DuckDB’s ListColumnReader will iterate through the Parquet definition/repetition levels and build the DuckDB list vector gradually. It directly populates the list_entry_t offsets and lengths for each row and appends child elements in batches. Notably, the Parquet reader uses a chunk size (e.g. 2048 by default) and will split very large lists across multiple output chunks if necessary. In the code, there’s a loop that reads up to a vector of child elements at a time; “if an individual list is longer than STANDARD_VECTOR_SIZE we actually have to loop the child read to fill it”
fossies.org
. If the current output chunk fills up before finishing a list, the reader stores the remaining elements in an overflow buffer to carry them into the next chunk
fossies.org
. This way, even a single huge list (with more than 2048 elements) is handled across chunk boundaries without needing to allocate one giant vector. The relevant code shows that when result_offset (number of list entries output) hits the chunk limit, it breaks and slices the leftover child values into an overflow buffer for the next call
fossies.org
. When reading a nested list, the Parquet reader uses Parquet’s repetition levels to determine whether a value continues an existing list or starts a new list. In the inner loop, if the Parquet repetition_level == max_repeat for a value, it means it’s part of the same list as the previous element – DuckDB then calls HandleRepeat to increment the length of the last list entry
fossies.org
. If instead a new list starts (lower repetition), DuckDB finalizes the previous list and begins a new one: it calls HandleListStart to set a new list entry with the current child offset and initial length, or HandleNull if the definition levels indicate the list is NULL
fossies.org
. For example, in the code a non-NULL list start is recorded with length 1 (to be extended if more elements follow), an empty list is recorded with length 0, and a NULL list marks that row as invalid and length 0
fossies.org
. After processing the batch of child values, the reader appends them to the list’s child vector in one go – in C++ this is done via ListVector::Append, which concatenates the read_vector of child elements to the list’s child vector
fossies.org
 (in the C API, you would use duckdb_list_vector_set_size to increase the child size accordingly). Structs are handled similarly but on multiple child columns. DuckDB’s Parquet reader has a StructColumnReader that orchestrates reading each field’s ColumnReader into the corresponding child vector of the Struct. It iterates over each struct field: if a field is not required by the query (projection pushdown), the Parquet reader doesn’t even instantiate a reader for it – DuckDB just produces a constant NULL vector for that field
fossies.org
. For the needed fields, it calls each child reader’s Read(...) function to fill that field’s vector. All children are read for the same rows count, and the struct’s own validity mask is then derived from Parquet definition levels: if the definition level for a struct value is below the max (meaning the entire struct was NULL in the source), DuckDB marks that struct vector index as NULL
fossies.org
. Essentially, a struct is null if any parent in the hierarchy was null. The Parquet reader uses the define_out array (definition levels) to decide this
fossies.org
. If a struct is not null, its fields may still contain nulls independently (tracked in each child’s validity). Memory management and pushdown: The Parquet reader tries to minimize in-memory data by projection pushdown and chunked reading. DuckDB’s table function API allows the Parquet table function to declare it supports pushdown, so it knows which columns (or struct fields) are actually needed
docs.rs
docs.rs
. Unused fields are skipped as noted (set to NULL without reading) to avoid unnecessary work and memory use. However, for the fields that are needed, the extension will read all their data from Parquet into DuckDB’s vectors. There isn’t an aggregate pushdown for something like SUM() of list elements – the Parquet reader doesn’t compute aggregates internally. It loads the relevant list/vector in full, and DuckDB’s query engine handles the aggregation or unnesting. In other words, operations like UNNEST() or summing list elements happen on the DuckDB side using the vectors in memory, not within the Parquet reader. The reader’s job is to efficiently translate Parquet’s nested representation into DuckDB’s in-memory format (with appropriate offsets, validity, etc.), possibly skipping entire columns if not needed, but it doesn’t skip individual elements based on query operations like SUM. (One exception is filter pushdown: DuckDB can push down row group or column predicates to Parquet, but for aggregation on list elements, there’s no such pushdown – all list elements still need to be read into memory so the engine can aggregate them.)
Query-Time Handling of Nested Data (UNNEST & Aggregates)
Once the Parquet (or JSON) extension has produced the nested column in DuckDB’s format, DuckDB’s execution engine can operate on it with its native SQL features. The UNNEST() function (in a SELECT clause or a lateral join) will take a list vector and produce a relation where each element becomes a row. Internally, this is done without copying the element data whenever possible – the engine can use the list’s child vector directly. For example, DuckDB can “explode” a list by leveraging the offset/length arrays to index into the child vector, and using a selection vector to repeat any correlated columns appropriately
duckdb.org
duckdb.org
. This means the heavy lifting of turning nested data into flat rows is done at query time, but it reuses the in-memory structures efficiently. Summaries or aggregates on lists (e.g. summing all elements) are typically achieved by unnesting or by functions like list_sum (DuckDB’s approach would be SELECT SUM(x) FROM tbl, UNNEST(tbl.list_col) AS x). In all cases, the engine will iterate over the list’s child values or apply vectorized operations/lambdas on them, rather than the file reader doing it. Thus, the Parquet/JSON extension does need to read in all the relevant list elements from the file into memory (subject to pushdown filtering of entire columns), and then DuckDB can perform the aggregation or unnest. There isn’t logic in the Parquet reader to compute, say, a partial sum to avoid storing the whole list – it hands off raw values to DuckDB, which then may aggregate them.
Implementing a Custom JSON Reader in Rust (Nested Data Guide)
When writing a DuckDB extension in Rust to ingest JSON with nested structures, you should mimic the above approach: produce DuckDB’s nested vectors on a chunk-by-chunk basis, without loading everything at once, and use the DuckDB vector API to handle nesting. Here’s a guide to doing this:
Use DuckDB’s Table Function Interface: In the Rust extension, you’ll likely register a table function (e.g. read_json). DuckDB’s extension API (C or Rust FFI) will call your function in an iterative way to produce chunks of rows. For example, if using the duckdb_extension_framework, you define a TableFunction with an init (to open file/initialize parser) and a function callback that fills a DataChunk with up to STANDARD_VECTOR_SIZE rows each time
docs.rs
docs.rs
. The framework will handle calling you repeatedly until you indicate no more rows (e.g. by returning 0 rows). Ensure you enable projection pushdown for this table function (via supports_pushdown(true)) so you can skip unnecessary JSON fields
docs.rs
. In your init stage, the DuckDB API can tell you which columns/fields are requested by the query (e.g. InitInfo::get_column_indices in the Rust framework)
docs.rs
. Use this to avoid parsing JSON fields that aren’t needed: you can allocate vectors only for required columns or simply not fill unused ones (setting them NULL as DuckDB does for Parquet).
Setting Up Vectors for Nested Types: When the function is called to produce the next chunk, you will receive or create a DataChunk with a set of column vectors corresponding to your JSON schema. Each column’s logical type might be a primitive (INT, VARCHAR, etc.) or a nested type (STRUCT, LIST). The DuckDB Rust API mirrors the C API: e.g., there is a duckdb_vector handle or a Vector struct. For a struct column, you won’t call duckdb_vector_get_data (since there’s no flat data array) – instead, retrieve each child column’s vector with duckdb_struct_vector_get_child(parent, field_index)
duckdb.org
. For a list column, call duckdb_list_vector_get_child(list_col) to get its child element vector
duckdb.org
. Also fetch the pointer to the list entries array: auto *entries = (duckdb_list_entry *) duckdb_vector_get_data(list_col) (in Rust, likely by casting the void pointer from duckdb_vector_get_data). Each duckdb_list_entry has offset and length fields
duckdb.org
 which you will set for each row’s list.
Filling Primitive Values: For non-nested types (or the leaf elements of a nested type), you can get a direct pointer to the vector’s data array with duckdb_vector_get_data. For example, an INT vector gives you an int32_t* to write into. Likewise, fetch the validity mask (duckdb_vector_get_validity) to set NULLs where needed (or use the helper duckdb_validity_set_row_invalid via FFI). The DuckDB vector length (capacity) is typically 2048 by default, but can expand if needed – writing up to the chunk size is fine. Do not exceed the vector’s capacity without resizing; DuckDB provides duckdb_list_vector_set_size() to extend a list’s child vector length if you append a lot of elements
duckdb.org
.
Filling Structs: If your JSON has an object (struct) field, treat each sub-field as its own output vector. For each output row, if the JSON object is present, parse each sub-field and write the value into the corresponding child vector (or mark NULL if that field is missing in the JSON). If the entire object is null or missing, you should mark the struct’s validity as NULL for that row
fossies.org
. That means: set the struct vector’s validity bit to 0, and you don’t need to fill any child values for that row (DuckDB will consider the whole struct NULL, and typically ignore child values). In practice, you can simply not touch the child vectors for that row (or set them to some default), as long as the struct validity is NULL. The Parquet reader uses definition levels to decide this
fossies.org
; in your JSON reader, you’ll do it based on whether the JSON field is present. (If some sub-fields are missing but others exist, that’s just a NULL in the child’s validity, not a NULL struct – DuckDB structs allow partial null fields with a valid parent struct.)
Filling Lists: For a JSON array, you will produce a list entry per row. When parsing a JSON array for row i:
If the JSON value is null (or the field is absent and treated as NULL), mark the list vector’s validity as NULL at index i, and you can set that entry’s offset=0, length=0 (DuckDB ignores offset/length for NULL entries)
fossies.org
.
If it’s an empty array, the entry should be valid (not NULL) but have length=0. You can set offset to the current end of the child vector (which doesn’t advance in this case) and length=0
fossies.org
.
If it’s a non-empty array, allocate space in the child vector for all its elements. Let’s say the list has N elements. You should append those N values to the list’s child vector. Determine the starting offset: in DuckDB’s list format, all children are in one big array, so the offset for row i will be the current total count of elements that have been appended so far (for previous rows in the chunk). You can get the current child vector size via duckdb_list_vector_get_size(list_col)
duckdb.org
 – or maintain a running counter. Set the entries[i].offset to that current size. Then write the array’s elements into the child vector sequentially, and finally set entries[i].length = N. After writing the elements, update the child vector size: duckdb_list_vector_set_size(list_col, new_size) where new_size = old_size + N
duckdb.org
. This tells DuckDB how many child entries are valid. Mark any element’s validity as NULL if the JSON element was null (for nested nulls inside the array).
Tip: You can parse the JSON array directly into the child vector’s memory to avoid extra copies. For example, get a pointer to the child vector’s data (child_ptr = duckdb_vector_get_data(child_vector)) of the appropriate type and fill it in a loop. Or use DuckDB’s Value API to construct values, but that’s less efficient than direct writes.
Chunking and Large Lists: Aim to produce at most VECTOR_SIZE (2048) rows per chunk. If your JSON file is huge, you’ll parse it in a streaming fashion: read some number of JSON objects, output a chunk, then continue. The tricky part is if a single JSON list has more elements than you can reasonably put in one chunk’s child vector. DuckDB’s vector size is not a hard limit for child vectors – the child can actually exceed 2048 if one row has a very large list, as long as you call duckdb_list_vector_set_size to extend it. But it’s wise to mimic the Parquet approach: you can break a very large list across chunks. For example, suppose row i has a list of 10,000 elements and your nominal chunk size is 1024 rows. Rather than outputting 1 row with 10k children (which might bloat memory), you could output the first portion of that list in the current chunk and the rest in the next chunk. This is advanced to implement, but Parquet’s reader does exactly that by using an overflow buffer
fossies.org
. One strategy is: if adding a list’s elements would exceed some threshold (like the child vector would go beyond, say, 2048 or 4096 elements), you can cut it off. Emit the current chunk with that row i having a partial list (e.g., 2048 elements). In the next chunk, continue the same row i (technically the same logical row) – this might require outputting a “continuation” row. However, note that DuckDB’s execution model expects each chunk’s rows to represent distinct table rows; splitting one JSON object across multiple chunks is not trivial. Parquet gets away with it because it’s reading from a column store with repetition levels, but as an independent JSON reader, you might instead choose to allocate a bigger buffer for that one row’s list. In practice, it might be acceptable to let a single list exceed the vector length, since DuckDB’s internal code will resize the vector backing memory if needed. The safer course: call duckdb_list_vector_set_size to extend the child vector for as many elements as you parsed. DuckDB will reallocate the child vector’s buffer if the new size is larger than the current capacity. This way, you can handle a large list in one chunk. (Just be mindful of memory – extremely large lists could still be an issue, but at least you aren’t forced to copy the whole JSON file into memory, only one list’s contents at a time.)
Finalizing the Chunk: After filling all columns for up to the chunk’s row limit or until the JSON input is exhausted, you return control to DuckDB. If using the C API, you’d indicate how many rows were added to the DataChunk. In Rust’s extension framework, you might write into the provided DataChunk object directly. DuckDB will then process that chunk (apply filters, join, etc.) and call your function again for the next chunk. You repeat the parsing for the next set of rows. Continue until you hit end-of-file, then signal that no more chunks are available (e.g. return 0 rows).
Code References and Examples: It’s useful to study DuckDB’s existing readers. The Parquet extension’s source shows how list offsets and lengths are populated and how child data is appended. For instance, the list reader sets each list entry with an offset and initial length, then uses ListVector::Append to append the child values and update lengths as elements repeat
fossies.org
fossies.org
. The struct reader demonstrates reading multiple children and marking struct validity
fossies.org
fossies.org
. While those are C++ internals, the DuckDB C API provides equivalent functionality: e.g., the methods duckdb_list_vector_get_child / _set_size and duckdb_struct_vector_get_child correspond to the C++ ListVector/StructVector helpers
duckdb.org
duckdb.org
. DuckDB’s documentation also includes a C example of reading a struct vector, which shows retrieving child vectors and checking validity
duckdb.org
duckdb.org
. You can use these to guide your Rust implementation via FFI. Essentially, your Rust extension will populate the DataChunk vectors in the same way DuckDB’s own code would – by navigating into nested child vectors and writing data at the appropriate offsets. By following the Parquet approach (streaming parsing, projection pushdown, and careful handling of offsets/lengths), you can efficiently “shred” JSON into DuckDB’s columnar format
duckdb.org
duckdb.org
 without loading the entire JSON into memory at once.
Sources: The DuckDB vector API reference and Parquet/JSON implementation code were consulted for these guidelines. Key references include DuckDB’s documentation on vectors (for list/struct handling)
duckdb.org
duckdb.org
, the Parquet extension’s list reader logic
fossies.org
fossies.org
 and struct reader logic
fossies.org
fossies.org
, as well as DuckDB’s JSON extension blog which outlines how JSON is parsed into DuckDB’s native types
duckdb.org
duckdb.org
. These illustrate how DuckDB expects nested data to be represented and processed. By emulating these patterns in Rust, your custom JSON reader can achieve similar efficiency and correctness in handling deeply nested data.