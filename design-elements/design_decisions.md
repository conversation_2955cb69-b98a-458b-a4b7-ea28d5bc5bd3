# Design Decisions

## Core Architecture

### Recursive Streaming Architecture
- Single recursive function handles all JSON value types uniformly
- Enables arbitrary nesting depth without hardcoded limits
- Routes to appropriate vector insertion logic based on value type and expected schema

### Struson Streaming Parser
- Direct streaming from JSON to DuckDB vectors
- Achieves O(row_size) memory usage
- Temporary structures only for collection during streaming

### DuckDB Vector Management
- Arrays of Objects: Use `list_vector.struct_child(capacity)`
- Struct Fields: Use `struct_vector.child(field_idx, capacity)`
- Multi-dimensional Arrays: Recursive list vector management
- Each array element gets its own memory location

### Schema Discovery
- Recursive type analysis of first element
- Schema-driven processing for type safety
- Handles arbitrary nesting depth

### Schema Validation
- Strict schema validation with clear error messages
- Mixed-depth arrays rejected as schema conflicts
- Ensures data quality and type safety

## Implementation Principles

### Recursive Processing
- Use recursive helper functions for arbitrary depth
- Avoid hardcoded depth limits
- Break into focused helper functions with clear responsibilities

### Type System Integrity
- Always use proper DuckDB types (STRUCT, LIST)
- Preserve all valid JSON structures with complete data
- Schema-driven processing for type safety

## Type Mapping
- JSON String → DuckDB VARCHAR
- JSON Number → DuckDB DOUBLE
- JSON Boolean → DuckDB BOOLEAN
- JSON Object → DuckDB STRUCT
- JSON Array → DuckDB LIST
- JSON null → DuckDB NULL

## Root-Level Array Flattening
- Array of objects → Multiple rows with columns
- Array of primitives → Multiple rows, single column
- Array of arrays → Multiple rows, array columns
- Single objects → Single row with columns
- Deep nesting preserved using DuckDB native types

## Error Handling
- Fail-fast on malformed JSON
- Clear failure modes for debugging
