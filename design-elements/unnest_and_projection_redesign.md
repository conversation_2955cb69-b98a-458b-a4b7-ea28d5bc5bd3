# UNNEST and Projection Pushdown Design

## DuckDB List Vector Requirements

### UNNEST Support
- Use `list_vector.set_entry(row, offset, length)` for array elements
- Call `list_vector.set_len(total_child_elements)` for UNNEST compatibility
- Each list vector manages its own child vector independently

### Projection Mapping
- Map schema column indices to output vector indices
- Handle cases where projected columns are subset of schema columns

## Path-Based Projection System

### Nested Projection Support
- Support projection at all nesting levels
- Skip entire JSON subtrees not in projection
- Create vectors only for projected paths
- Enable streaming with minimal memory usage

### JSON Parser Integration
- Use struson's streaming parser with path-based filtering
- Skip parsing of non-projected JSON segments
- Only parse JSON paths that are actually needed

## Implementation Approach

### List Vector Management
- Use native DuckDB list vector patterns
- No global offset coordination needed
- Each list vector manages child vectors independently

### Memory Optimization
- Dynamic vector creation for projected paths only
- Avoid pre-allocating vectors for entire document schema
- Maintain O(row_size) memory usage
