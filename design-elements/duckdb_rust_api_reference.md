# DuckDB Rust API Reference

## Vector Types
```rust
pub struct FlatVector    // Primitive types
pub struct ListVector    // Variable-length arrays
pub struct StructVector  // Named field collections
```

## ListVector API
```rust
// Child vector access
list_vector.child(capacity);        // → FlatVector (primitives)
list_vector.struct_child(capacity); // → StructVector (objects)
list_vector.list_child();           // → ListVector (nested arrays)

// List management
list_vector.set_entry(idx, offset, length);
list_vector.set_len(total_elements);
```

## StructVector API
```rust
// Field access
struct_vector.child(field_idx, capacity);        // → FlatVector (primitive field)
struct_vector.struct_vector_child(field_idx);    // → StructVector (nested struct)
struct_vector.list_vector_child(field_idx);      // → ListVector (array field)
```

## Type Construction
```rust
// Primitive types
LogicalTypeHandle::from(LogicalTypeId::Double);
LogicalTypeHandle::from(LogicalTypeId::Varchar);

// Complex types
LogicalTypeHandle::list(&child_type);
LogicalTypeHandle::struct_type(&[("field", type)]);
```

## Data Insertion
```rust
// Primitive data
flat_vector.as_mut_slice::<f64>()[idx] = value;
flat_vector.insert(idx, &string_value);
flat_vector.set_null(idx);
```
