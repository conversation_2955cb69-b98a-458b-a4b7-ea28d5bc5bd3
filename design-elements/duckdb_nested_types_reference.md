# DuckDB Nested Types API Reference

## Vector Types
- **FlatVector**: Primitive types (numbers, strings, booleans)
- **ListVector**: Variable-length arrays  
- **StructVector**: Named field collections

## ListVector API
```rust
// Memory management
list_vector.set_len(total_elements);
list_vector.set_entry(idx, offset, length);

// Child vector access
list_vector.child(capacity);        // → FlatVector (primitives)
list_vector.struct_child(capacity); // → StructVector (objects)
list_vector.list_child();           // → ListVector (nested arrays)
```

## StructVector API
```rust
// Field access by index
struct_vector.child(field_idx, capacity);        // → FlatVector (primitive field)
struct_vector.struct_vector_child(field_idx);    // → StructVector (nested struct)
struct_vector.list_vector_child(field_idx);      // → ListVector (array field)
```

## Type Construction
```rust
// Primitive types
LogicalTypeHandle::from(LogicalTypeId::Double);
LogicalTypeHandle::from(LogicalTypeId::Varchar);

// Complex types  
LogicalTypeHandle::list(&child_type);                    // LIST[child_type]
LogicalTypeHandle::struct_type(&[("field", type)]);      // STRUCT(field type)
```

## FlatVector API
```rust
// Data insertion
flat_vector.as_mut_slice::<f64>()[idx] = value;         // Numeric data
flat_vector.insert(idx, &string_value);                 // String data
flat_vector.set_null(idx);                              // Null values
```
